# Importar o client
from qdrant_client import QdrantClient

# <PERSON>riar nossa coleção
from qdrant_client.http.models import Distance, VectorParams

# Criar nossos vetores
from qdrant_client.http.models import PointStruct

# Filtrar payloads
from qdrant_client.http.models import Filter, FieldCondition, MatchValue

# Pretty Printer
from pprint import pprint

client = QdrantClient(url="http://localhost:6333", api_key="my-secret-key")

# client.create_collection(
#     collection_name="test_collection",
#     vectors_config=VectorParams(size=4, distance=Distance.DOT),
# )


# operation_info = client.upsert(
#     collection_name="test_collection",
#     wait=True,
#     points=[
#         PointStruct(id=1, vector=[0.05, 0.61, 0.76, 0.74], payload={"continente": "Europa", "cidade": "Berlin"}),
#         PointStruct(id=2, vector=[0.19, 0.81, 0.75, 0.11], payload={"continente": "Europa", "cidade": "Londres"}),
#         PointStruct(id=4, vector=[0.18, 0.01, 0.85, 0.80], payload={"continente": "America", "cidade": "Nova York"}),
#         PointStruct(id=5, vector=[0.24, 0.18, 0.22, 0.44], payload={"continente": "Asia", "cidade": "Beijing"}),
#         PointStruct(id=6, vector=[0.35, 0.08, 0.11, 0.44], payload={"continente": "Asia", "cidade": "Mumbai"}),
#     ],
# )

# pprint(operation_info)

# search_result = client.search(
#     collection_name="test_collection", query_vector=[0.2, 0.1, 0.9, 0.7], limit=3
# )

# pprint(search_result)


search_result = client.search(
    collection_name="test_collection",
    query_vector=[0.2, 0.1, 0.9, 0.7],
    query_filter=Filter(
        must=[FieldCondition(key="continente", match=MatchValue(value="Asia"))]
    ),
    with_payload=True,
    limit=3,
)

pprint(search_result)