# QDRANT
## Importar o client
from qdrant_client import QdrantClient
## Criar nossa coleção
from qdrant_client.http.models import Distance, VectorParams


# LANGCHAIN
## Importar Qdrant como vector store
from langchain.vectorstores import Qdrant
## Importar OpenAI embeddings
from langchain.embeddings.openai import OpenAIEmbeddings
## Função para auxiliar na quebra do texto em chunks
from langchain.text_splitter import CharacterTextSplitter
## Módulo para facilitar o uso de vector stores em QA (question answering)
from langchain.chains import RetrievalQA
## Importar LLM
from langchain.llms import OpenAI


# PYTHON
# Variável de Ambiente
import os

client = QdrantClient(url="http://localhost:6333", api_key="my-secret-key")

# client.create_collection(
#     collection_name="openai_collection",
#     vectors_config=VectorParams(size=1536, distance=Distance.COSINE),
# )

os.environ['OPENAI_API_KEY'] = '********************************************************************************************************************************************************************'

embeddings = OpenAIEmbeddings()

vectorstore = Qdrant(
        client=client,
        collection_name="openai_collection",
        embeddings=embeddings
    )

# def get_chunks(text):
#     text_splitter = CharacterTextSplitter(
#         separator="\n",
#         chunk_size=1000,
#         chunk_overlap=200,
#         length_function=len
#     )
#     chunks = text_splitter.split_text(text)
#     return chunks

# with open("./base.txt") as f:
#     raw_text = f.read()

# texts = get_chunks(raw_text)

# vectorstore.add_texts(texts)

qa = RetrievalQA.from_chain_type(
    llm=OpenAI(),
    chain_type="stuff",
    retriever=vectorstore.as_retriever()
)