2025/10/02-02:32:57.278468 499 RocksDB version: 9.9.3
2025/10/02-02:32:57.279196 499 Compile date 2024-12-05 01:25:31
2025/10/02-02:32:57.279201 499 DB SUMMARY
2025/10/02-02:32:57.279206 499 Host name (Env):  62f3c49c1644
2025/10/02-02:32:57.279208 499 DB Session ID:  5WM5AVIYRFD3Y9QLSJOG
2025/10/02-02:32:57.282928 499 SST files in ./storage/collections/openai_collection/0/segments/78468bf3-148c-4475-aa1b-497ba8411b72/payload_index dir, Total Num: 0, files: 
2025/10/02-02:32:57.282934 499 Write Ahead Log file in ./storage/collections/openai_collection/0/segments/78468bf3-148c-4475-aa1b-497ba8411b72/payload_index: 
2025/10/02-02:32:57.282937 499                         Options.error_if_exists: 0
2025/10/02-02:32:57.282939 499                       Options.create_if_missing: 1
2025/10/02-02:32:57.282941 499                         Options.paranoid_checks: 1
2025/10/02-02:32:57.282943 499             Options.flush_verify_memtable_count: 1
2025/10/02-02:32:57.282946 499          Options.compaction_verify_record_count: 1
2025/10/02-02:32:57.282949 499                               Options.track_and_verify_wals_in_manifest: 0
2025/10/02-02:32:57.282952 499        Options.verify_sst_unique_id_in_manifest: 1
2025/10/02-02:32:57.282955 499                                     Options.env: 0x7150b5c21540
2025/10/02-02:32:57.282959 499                                      Options.fs: PosixFileSystem
2025/10/02-02:32:57.282965 499                                Options.info_log: 0x7150b5083f00
2025/10/02-02:32:57.282969 499                Options.max_file_opening_threads: 16
2025/10/02-02:32:57.282973 499                              Options.statistics: (nil)
2025/10/02-02:32:57.282980 499                               Options.use_fsync: 0
2025/10/02-02:32:57.282983 499                       Options.max_log_file_size: 1048576
2025/10/02-02:32:57.282986 499                  Options.max_manifest_file_size: 1073741824
2025/10/02-02:32:57.282989 499                   Options.log_file_time_to_roll: 0
2025/10/02-02:32:57.282992 499                       Options.keep_log_file_num: 1
2025/10/02-02:32:57.282994 499                    Options.recycle_log_file_num: 0
2025/10/02-02:32:57.282996 499                         Options.allow_fallocate: 1
2025/10/02-02:32:57.282998 499                        Options.allow_mmap_reads: 0
2025/10/02-02:32:57.283000 499                       Options.allow_mmap_writes: 0
2025/10/02-02:32:57.283001 499                        Options.use_direct_reads: 0
2025/10/02-02:32:57.283003 499                        Options.use_direct_io_for_flush_and_compaction: 0
2025/10/02-02:32:57.283005 499          Options.create_missing_column_families: 1
2025/10/02-02:32:57.283007 499                              Options.db_log_dir: 
2025/10/02-02:32:57.283008 499                                 Options.wal_dir: 
2025/10/02-02:32:57.283010 499                Options.table_cache_numshardbits: 6
2025/10/02-02:32:57.283012 499                         Options.WAL_ttl_seconds: 0
2025/10/02-02:32:57.283013 499                       Options.WAL_size_limit_MB: 0
2025/10/02-02:32:57.283015 499                        Options.max_write_batch_group_size_bytes: 1048576
2025/10/02-02:32:57.283017 499             Options.manifest_preallocation_size: 4194304
2025/10/02-02:32:57.283019 499                     Options.is_fd_close_on_exec: 1
2025/10/02-02:32:57.283020 499                   Options.advise_random_on_open: 1
2025/10/02-02:32:57.283022 499                    Options.db_write_buffer_size: 0
2025/10/02-02:32:57.283023 499                    Options.write_buffer_manager: 0x7150b5005600
2025/10/02-02:32:57.283025 499           Options.random_access_max_buffer_size: 1048576
2025/10/02-02:32:57.283027 499                      Options.use_adaptive_mutex: 0
2025/10/02-02:32:57.283028 499                            Options.rate_limiter: (nil)
2025/10/02-02:32:57.283031 499     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/10/02-02:32:57.283032 499                       Options.wal_recovery_mode: 0
2025/10/02-02:32:57.283034 499                  Options.enable_thread_tracking: 0
2025/10/02-02:32:57.283036 499                  Options.enable_pipelined_write: 0
2025/10/02-02:32:57.283046 499                  Options.unordered_write: 0
2025/10/02-02:32:57.283048 499         Options.allow_concurrent_memtable_write: 1
2025/10/02-02:32:57.283049 499      Options.enable_write_thread_adaptive_yield: 1
2025/10/02-02:32:57.283051 499             Options.write_thread_max_yield_usec: 100
2025/10/02-02:32:57.283053 499            Options.write_thread_slow_yield_usec: 3
2025/10/02-02:32:57.283057 499                               Options.row_cache: None
2025/10/02-02:32:57.283059 499                              Options.wal_filter: None
2025/10/02-02:32:57.283060 499             Options.avoid_flush_during_recovery: 0
2025/10/02-02:32:57.283062 499             Options.allow_ingest_behind: 0
2025/10/02-02:32:57.283064 499             Options.two_write_queues: 0
2025/10/02-02:32:57.283065 499             Options.manual_wal_flush: 0
2025/10/02-02:32:57.283067 499             Options.wal_compression: 0
2025/10/02-02:32:57.283072 499             Options.background_close_inactive_wals: 0
2025/10/02-02:32:57.283073 499             Options.atomic_flush: 0
2025/10/02-02:32:57.283075 499             Options.avoid_unnecessary_blocking_io: 0
2025/10/02-02:32:57.283077 499             Options.prefix_seek_opt_in_only: 0
2025/10/02-02:32:57.283078 499                 Options.persist_stats_to_disk: 0
2025/10/02-02:32:57.283080 499                 Options.write_dbid_to_manifest: 1
2025/10/02-02:32:57.283082 499                 Options.write_identity_file: 1
2025/10/02-02:32:57.283083 499                 Options.log_readahead_size: 0
2025/10/02-02:32:57.283085 499                 Options.file_checksum_gen_factory: Unknown
2025/10/02-02:32:57.283087 499                 Options.best_efforts_recovery: 0
2025/10/02-02:32:57.283088 499                Options.max_bgerror_resume_count: 2147483647
2025/10/02-02:32:57.283090 499            Options.bgerror_resume_retry_interval: 1000000
2025/10/02-02:32:57.283095 499             Options.allow_data_in_errors: 0
2025/10/02-02:32:57.283096 499             Options.db_host_id: __hostname__
2025/10/02-02:32:57.283098 499             Options.enforce_single_del_contracts: true
2025/10/02-02:32:57.283100 499             Options.metadata_write_temperature: kUnknown
2025/10/02-02:32:57.283102 499             Options.wal_write_temperature: kUnknown
2025/10/02-02:32:57.283104 499             Options.max_background_jobs: 2
2025/10/02-02:32:57.283105 499             Options.max_background_compactions: -1
2025/10/02-02:32:57.283107 499             Options.max_subcompactions: 1
2025/10/02-02:32:57.283109 499             Options.avoid_flush_during_shutdown: 0
2025/10/02-02:32:57.283110 499           Options.writable_file_max_buffer_size: 1048576
2025/10/02-02:32:57.283112 499             Options.delayed_write_rate : 16777216
2025/10/02-02:32:57.283114 499             Options.max_total_wal_size: 0
2025/10/02-02:32:57.283116 499             Options.delete_obsolete_files_period_micros: 180000000
2025/10/02-02:32:57.283117 499                   Options.stats_dump_period_sec: 600
2025/10/02-02:32:57.283119 499                 Options.stats_persist_period_sec: 600
2025/10/02-02:32:57.283121 499                 Options.stats_history_buffer_size: 1048576
2025/10/02-02:32:57.283122 499                          Options.max_open_files: 256
2025/10/02-02:32:57.283124 499                          Options.bytes_per_sync: 0
2025/10/02-02:32:57.283126 499                      Options.wal_bytes_per_sync: 0
2025/10/02-02:32:57.283127 499                   Options.strict_bytes_per_sync: 0
2025/10/02-02:32:57.283129 499       Options.compaction_readahead_size: 2097152
2025/10/02-02:32:57.283131 499                  Options.max_background_flushes: -1
2025/10/02-02:32:57.283132 499 Options.daily_offpeak_time_utc: 
2025/10/02-02:32:57.283134 499 Compression algorithms supported:
2025/10/02-02:32:57.283136 499 	kZSTD supported: 0
2025/10/02-02:32:57.283138 499 	kXpressCompression supported: 0
2025/10/02-02:32:57.283140 499 	kBZip2Compression supported: 0
2025/10/02-02:32:57.283144 499 	kZSTDNotFinalCompression supported: 0
2025/10/02-02:32:57.283146 499 	kLZ4Compression supported: 1
2025/10/02-02:32:57.283148 499 	kZlibCompression supported: 0
2025/10/02-02:32:57.283149 499 	kLZ4HCCompression supported: 1
2025/10/02-02:32:57.283151 499 	kSnappyCompression supported: 1
2025/10/02-02:32:57.283154 499 Fast CRC32 supported: Not supported on x86
2025/10/02-02:32:57.283156 499 DMutex implementation: pthread_mutex_t
2025/10/02-02:32:57.283157 499 Jemalloc supported: 0
2025/10/02-02:32:57.365525 499               Options.comparator: leveldb.BytewiseComparator
2025/10/02-02:32:57.365531 499           Options.merge_operator: None
2025/10/02-02:32:57.365535 499        Options.compaction_filter: None
2025/10/02-02:32:57.365538 499        Options.compaction_filter_factory: None
2025/10/02-02:32:57.365540 499  Options.sst_partitioner_factory: None
2025/10/02-02:32:57.365543 499         Options.memtable_factory: SkipListFactory
2025/10/02-02:32:57.365546 499            Options.table_factory: BlockBasedTable
2025/10/02-02:32:57.365671 499            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7150b50066c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7150b5013610
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/02-02:32:57.365723 499        Options.write_buffer_size: 10485760
2025/10/02-02:32:57.365727 499  Options.max_write_buffer_number: 2
2025/10/02-02:32:57.365733 499          Options.compression: LZ4
2025/10/02-02:32:57.365736 499                  Options.bottommost_compression: Disabled
2025/10/02-02:32:57.365739 499       Options.prefix_extractor: nullptr
2025/10/02-02:32:57.365742 499   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/02-02:32:57.365773 499             Options.num_levels: 7
2025/10/02-02:32:57.365777 499        Options.min_write_buffer_number_to_merge: 1
2025/10/02-02:32:57.365779 499     Options.max_write_buffer_number_to_maintain: 0
2025/10/02-02:32:57.365781 499     Options.max_write_buffer_size_to_maintain: 0
2025/10/02-02:32:57.365783 499            Options.bottommost_compression_opts.window_bits: -14
2025/10/02-02:32:57.365785 499                  Options.bottommost_compression_opts.level: 32767
2025/10/02-02:32:57.365787 499               Options.bottommost_compression_opts.strategy: 0
2025/10/02-02:32:57.365788 499         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/02-02:32:57.365790 499         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/02-02:32:57.365792 499         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/02-02:32:57.365794 499                  Options.bottommost_compression_opts.enabled: false
2025/10/02-02:32:57.365796 499         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/02-02:32:57.365800 499         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/02-02:32:57.365802 499            Options.compression_opts.window_bits: -14
2025/10/02-02:32:57.365805 499                  Options.compression_opts.level: 32767
2025/10/02-02:32:57.365858 499               Options.compression_opts.strategy: 0
2025/10/02-02:32:57.365863 499         Options.compression_opts.max_dict_bytes: 0
2025/10/02-02:32:57.365866 499         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/02-02:32:57.365868 499         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/02-02:32:57.365871 499         Options.compression_opts.parallel_threads: 1
2025/10/02-02:32:57.365874 499                  Options.compression_opts.enabled: false
2025/10/02-02:32:57.365876 499         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/02-02:32:57.365879 499      Options.level0_file_num_compaction_trigger: 4
2025/10/02-02:32:57.365881 499          Options.level0_slowdown_writes_trigger: 20
2025/10/02-02:32:57.365884 499              Options.level0_stop_writes_trigger: 36
2025/10/02-02:32:57.365887 499                   Options.target_file_size_base: 67108864
2025/10/02-02:32:57.365890 499             Options.target_file_size_multiplier: 1
2025/10/02-02:32:57.365892 499                Options.max_bytes_for_level_base: 268435456
2025/10/02-02:32:57.365895 499 Options.level_compaction_dynamic_level_bytes: 1
2025/10/02-02:32:57.365900 499          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/02-02:32:57.365903 499 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/02-02:32:57.365906 499 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/02-02:32:57.365909 499 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/02-02:32:57.365911 499 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/02-02:32:57.365914 499 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/02-02:32:57.365917 499 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/02-02:32:57.365919 499 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/02-02:32:57.365925 499       Options.max_sequential_skip_in_iterations: 8
2025/10/02-02:32:57.365927 499                    Options.max_compaction_bytes: 1677721600
2025/10/02-02:32:57.365929 499                        Options.arena_block_size: 1048576
2025/10/02-02:32:57.365931 499   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/02-02:32:57.365938 499   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/02-02:32:57.365940 499                Options.disable_auto_compactions: 0
2025/10/02-02:32:57.365943 499                        Options.compaction_style: kCompactionStyleLevel
2025/10/02-02:32:57.365945 499                          Options.compaction_pri: kMinOverlappingRatio
2025/10/02-02:32:57.365946 499 Options.compaction_options_universal.size_ratio: 1
2025/10/02-02:32:57.365948 499 Options.compaction_options_universal.min_merge_width: 2
2025/10/02-02:32:57.365949 499 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/02-02:32:57.365951 499 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/02-02:32:57.365953 499 Options.compaction_options_universal.compression_size_percent: -1
2025/10/02-02:32:57.365955 499 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/02-02:32:57.365957 499 Options.compaction_options_universal.max_read_amp: -1
2025/10/02-02:32:57.365958 499 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/02-02:32:57.365960 499 Options.compaction_options_fifo.allow_compaction: 0
2025/10/02-02:32:57.365963 499                   Options.table_properties_collectors: 
2025/10/02-02:32:57.365965 499                   Options.inplace_update_support: 0
2025/10/02-02:32:57.365966 499                 Options.inplace_update_num_locks: 10000
2025/10/02-02:32:57.365969 499               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/02-02:32:57.365971 499               Options.memtable_whole_key_filtering: 0
2025/10/02-02:32:57.365972 499   Options.memtable_huge_page_size: 0
2025/10/02-02:32:57.365973 499                           Options.bloom_locality: 0
2025/10/02-02:32:57.365975 499                    Options.max_successive_merges: 0
2025/10/02-02:32:57.366018 499             Options.strict_max_successive_merges: 0
2025/10/02-02:32:57.366021 499                Options.optimize_filters_for_hits: 0
2025/10/02-02:32:57.366022 499                Options.paranoid_file_checks: 0
2025/10/02-02:32:57.366024 499                Options.force_consistency_checks: 1
2025/10/02-02:32:57.366025 499                Options.report_bg_io_stats: 0
2025/10/02-02:32:57.366027 499                               Options.ttl: 2592000
2025/10/02-02:32:57.366028 499          Options.periodic_compaction_seconds: 0
2025/10/02-02:32:57.366030 499                        Options.default_temperature: kUnknown
2025/10/02-02:32:57.366032 499  Options.preclude_last_level_data_seconds: 0
2025/10/02-02:32:57.366033 499    Options.preserve_internal_time_seconds: 0
2025/10/02-02:32:57.366035 499                       Options.enable_blob_files: false
2025/10/02-02:32:57.366037 499                           Options.min_blob_size: 0
2025/10/02-02:32:57.366038 499                          Options.blob_file_size: 268435456
2025/10/02-02:32:57.366040 499                   Options.blob_compression_type: NoCompression
2025/10/02-02:32:57.366042 499          Options.enable_blob_garbage_collection: false
2025/10/02-02:32:57.366044 499      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/02-02:32:57.366046 499 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/02-02:32:57.366048 499          Options.blob_compaction_readahead_size: 0
2025/10/02-02:32:57.366050 499                Options.blob_file_starting_level: 0
2025/10/02-02:32:57.366052 499         Options.experimental_mempurge_threshold: 0.000000
2025/10/02-02:32:57.366053 499            Options.memtable_max_range_deletions: 0
2025/10/02-02:32:57.478105 499 DB pointer 0x7150b5071800
