2025/10/02-02:32:57.274101 500 RocksDB version: 9.9.3
2025/10/02-02:32:57.274967 500 Compile date 2024-12-05 01:25:31
2025/10/02-02:32:57.274972 500 DB SUMMARY
2025/10/02-02:32:57.274977 500 Host name (Env):  62f3c49c1644
2025/10/02-02:32:57.274979 500 DB Session ID:  5WM5AVIYRFD3Y9QLSJOH
2025/10/02-02:32:57.279370 500 SST files in ./storage/collections/openai_collection/0/segments/4b931af5-5323-4b16-9d6a-2a8a4176770d/payload_index dir, Total Num: 0, files: 
2025/10/02-02:32:57.279378 500 Write Ahead Log file in ./storage/collections/openai_collection/0/segments/4b931af5-5323-4b16-9d6a-2a8a4176770d/payload_index: 
2025/10/02-02:32:57.279458 500                         Options.error_if_exists: 0
2025/10/02-02:32:57.279463 500                       Options.create_if_missing: 1
2025/10/02-02:32:57.279465 500                         Options.paranoid_checks: 1
2025/10/02-02:32:57.279467 500             Options.flush_verify_memtable_count: 1
2025/10/02-02:32:57.279469 500          Options.compaction_verify_record_count: 1
2025/10/02-02:32:57.279472 500                               Options.track_and_verify_wals_in_manifest: 0
2025/10/02-02:32:57.279474 500        Options.verify_sst_unique_id_in_manifest: 1
2025/10/02-02:32:57.279476 500                                     Options.env: 0x7150b5c21540
2025/10/02-02:32:57.279479 500                                      Options.fs: PosixFileSystem
2025/10/02-02:32:57.279481 500                                Options.info_log: 0x7150b4e83f00
2025/10/02-02:32:57.279483 500                Options.max_file_opening_threads: 16
2025/10/02-02:32:57.279485 500                              Options.statistics: (nil)
2025/10/02-02:32:57.279487 500                               Options.use_fsync: 0
2025/10/02-02:32:57.279488 500                       Options.max_log_file_size: 1048576
2025/10/02-02:32:57.279491 500                  Options.max_manifest_file_size: 1073741824
2025/10/02-02:32:57.279493 500                   Options.log_file_time_to_roll: 0
2025/10/02-02:32:57.279494 500                       Options.keep_log_file_num: 1
2025/10/02-02:32:57.279496 500                    Options.recycle_log_file_num: 0
2025/10/02-02:32:57.279498 500                         Options.allow_fallocate: 1
2025/10/02-02:32:57.279500 500                        Options.allow_mmap_reads: 0
2025/10/02-02:32:57.279502 500                       Options.allow_mmap_writes: 0
2025/10/02-02:32:57.279504 500                        Options.use_direct_reads: 0
2025/10/02-02:32:57.279506 500                        Options.use_direct_io_for_flush_and_compaction: 0
2025/10/02-02:32:57.279507 500          Options.create_missing_column_families: 1
2025/10/02-02:32:57.279509 500                              Options.db_log_dir: 
2025/10/02-02:32:57.279511 500                                 Options.wal_dir: 
2025/10/02-02:32:57.279512 500                Options.table_cache_numshardbits: 6
2025/10/02-02:32:57.279514 500                         Options.WAL_ttl_seconds: 0
2025/10/02-02:32:57.279516 500                       Options.WAL_size_limit_MB: 0
2025/10/02-02:32:57.279518 500                        Options.max_write_batch_group_size_bytes: 1048576
2025/10/02-02:32:57.279520 500             Options.manifest_preallocation_size: 4194304
2025/10/02-02:32:57.279522 500                     Options.is_fd_close_on_exec: 1
2025/10/02-02:32:57.279524 500                   Options.advise_random_on_open: 1
2025/10/02-02:32:57.279526 500                    Options.db_write_buffer_size: 0
2025/10/02-02:32:57.279528 500                    Options.write_buffer_manager: 0x7150b4e05600
2025/10/02-02:32:57.279530 500           Options.random_access_max_buffer_size: 1048576
2025/10/02-02:32:57.279531 500                      Options.use_adaptive_mutex: 0
2025/10/02-02:32:57.279533 500                            Options.rate_limiter: (nil)
2025/10/02-02:32:57.279535 500     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/10/02-02:32:57.279537 500                       Options.wal_recovery_mode: 0
2025/10/02-02:32:57.279539 500                  Options.enable_thread_tracking: 0
2025/10/02-02:32:57.279541 500                  Options.enable_pipelined_write: 0
2025/10/02-02:32:57.279588 500                  Options.unordered_write: 0
2025/10/02-02:32:57.279592 500         Options.allow_concurrent_memtable_write: 1
2025/10/02-02:32:57.279593 500      Options.enable_write_thread_adaptive_yield: 1
2025/10/02-02:32:57.279595 500             Options.write_thread_max_yield_usec: 100
2025/10/02-02:32:57.279597 500            Options.write_thread_slow_yield_usec: 3
2025/10/02-02:32:57.279601 500                               Options.row_cache: None
2025/10/02-02:32:57.279603 500                              Options.wal_filter: None
2025/10/02-02:32:57.279604 500             Options.avoid_flush_during_recovery: 0
2025/10/02-02:32:57.279606 500             Options.allow_ingest_behind: 0
2025/10/02-02:32:57.279608 500             Options.two_write_queues: 0
2025/10/02-02:32:57.279609 500             Options.manual_wal_flush: 0
2025/10/02-02:32:57.279611 500             Options.wal_compression: 0
2025/10/02-02:32:57.279616 500             Options.background_close_inactive_wals: 0
2025/10/02-02:32:57.279618 500             Options.atomic_flush: 0
2025/10/02-02:32:57.279620 500             Options.avoid_unnecessary_blocking_io: 0
2025/10/02-02:32:57.279622 500             Options.prefix_seek_opt_in_only: 0
2025/10/02-02:32:57.279624 500                 Options.persist_stats_to_disk: 0
2025/10/02-02:32:57.279626 500                 Options.write_dbid_to_manifest: 1
2025/10/02-02:32:57.279627 500                 Options.write_identity_file: 1
2025/10/02-02:32:57.279629 500                 Options.log_readahead_size: 0
2025/10/02-02:32:57.279631 500                 Options.file_checksum_gen_factory: Unknown
2025/10/02-02:32:57.279633 500                 Options.best_efforts_recovery: 0
2025/10/02-02:32:57.279634 500                Options.max_bgerror_resume_count: 2147483647
2025/10/02-02:32:57.279636 500            Options.bgerror_resume_retry_interval: 1000000
2025/10/02-02:32:57.279677 500             Options.allow_data_in_errors: 0
2025/10/02-02:32:57.279683 500             Options.db_host_id: __hostname__
2025/10/02-02:32:57.279685 500             Options.enforce_single_del_contracts: true
2025/10/02-02:32:57.279718 500             Options.metadata_write_temperature: kUnknown
2025/10/02-02:32:57.279722 500             Options.wal_write_temperature: kUnknown
2025/10/02-02:32:57.279724 500             Options.max_background_jobs: 2
2025/10/02-02:32:57.279727 500             Options.max_background_compactions: -1
2025/10/02-02:32:57.279728 500             Options.max_subcompactions: 1
2025/10/02-02:32:57.279730 500             Options.avoid_flush_during_shutdown: 0
2025/10/02-02:32:57.279732 500           Options.writable_file_max_buffer_size: 1048576
2025/10/02-02:32:57.279734 500             Options.delayed_write_rate : 16777216
2025/10/02-02:32:57.279735 500             Options.max_total_wal_size: 0
2025/10/02-02:32:57.279737 500             Options.delete_obsolete_files_period_micros: 180000000
2025/10/02-02:32:57.279739 500                   Options.stats_dump_period_sec: 600
2025/10/02-02:32:57.279741 500                 Options.stats_persist_period_sec: 600
2025/10/02-02:32:57.279743 500                 Options.stats_history_buffer_size: 1048576
2025/10/02-02:32:57.279744 500                          Options.max_open_files: 256
2025/10/02-02:32:57.279746 500                          Options.bytes_per_sync: 0
2025/10/02-02:32:57.279748 500                      Options.wal_bytes_per_sync: 0
2025/10/02-02:32:57.279750 500                   Options.strict_bytes_per_sync: 0
2025/10/02-02:32:57.279752 500       Options.compaction_readahead_size: 2097152
2025/10/02-02:32:57.279753 500                  Options.max_background_flushes: -1
2025/10/02-02:32:57.279755 500 Options.daily_offpeak_time_utc: 
2025/10/02-02:32:57.279757 500 Compression algorithms supported:
2025/10/02-02:32:57.279759 500 	kZSTD supported: 0
2025/10/02-02:32:57.279761 500 	kXpressCompression supported: 0
2025/10/02-02:32:57.279763 500 	kBZip2Compression supported: 0
2025/10/02-02:32:57.279805 500 	kZSTDNotFinalCompression supported: 0
2025/10/02-02:32:57.279809 500 	kLZ4Compression supported: 1
2025/10/02-02:32:57.279811 500 	kZlibCompression supported: 0
2025/10/02-02:32:57.279812 500 	kLZ4HCCompression supported: 1
2025/10/02-02:32:57.279814 500 	kSnappyCompression supported: 1
2025/10/02-02:32:57.279818 500 Fast CRC32 supported: Not supported on x86
2025/10/02-02:32:57.279821 500 DMutex implementation: pthread_mutex_t
2025/10/02-02:32:57.279822 500 Jemalloc supported: 0
2025/10/02-02:32:57.362903 500               Options.comparator: leveldb.BytewiseComparator
2025/10/02-02:32:57.362914 500           Options.merge_operator: None
2025/10/02-02:32:57.362917 500        Options.compaction_filter: None
2025/10/02-02:32:57.362919 500        Options.compaction_filter_factory: None
2025/10/02-02:32:57.362922 500  Options.sst_partitioner_factory: None
2025/10/02-02:32:57.362925 500         Options.memtable_factory: SkipListFactory
2025/10/02-02:32:57.362928 500            Options.table_factory: BlockBasedTable
2025/10/02-02:32:57.363140 500            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7150b4e06720)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x7150b4e13610
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 6
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/10/02-02:32:57.363148 500        Options.write_buffer_size: 10485760
2025/10/02-02:32:57.363150 500  Options.max_write_buffer_number: 2
2025/10/02-02:32:57.363154 500          Options.compression: LZ4
2025/10/02-02:32:57.363156 500                  Options.bottommost_compression: Disabled
2025/10/02-02:32:57.363158 500       Options.prefix_extractor: nullptr
2025/10/02-02:32:57.363160 500   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/10/02-02:32:57.363161 500             Options.num_levels: 7
2025/10/02-02:32:57.363163 500        Options.min_write_buffer_number_to_merge: 1
2025/10/02-02:32:57.363165 500     Options.max_write_buffer_number_to_maintain: 0
2025/10/02-02:32:57.363166 500     Options.max_write_buffer_size_to_maintain: 0
2025/10/02-02:32:57.363168 500            Options.bottommost_compression_opts.window_bits: -14
2025/10/02-02:32:57.363170 500                  Options.bottommost_compression_opts.level: 32767
2025/10/02-02:32:57.363172 500               Options.bottommost_compression_opts.strategy: 0
2025/10/02-02:32:57.363173 500         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/10/02-02:32:57.363175 500         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/10/02-02:32:57.363176 500         Options.bottommost_compression_opts.parallel_threads: 1
2025/10/02-02:32:57.363178 500                  Options.bottommost_compression_opts.enabled: false
2025/10/02-02:32:57.363180 500         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/10/02-02:32:57.363182 500         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/10/02-02:32:57.363183 500            Options.compression_opts.window_bits: -14
2025/10/02-02:32:57.363185 500                  Options.compression_opts.level: 32767
2025/10/02-02:32:57.363189 500               Options.compression_opts.strategy: 0
2025/10/02-02:32:57.363192 500         Options.compression_opts.max_dict_bytes: 0
2025/10/02-02:32:57.363194 500         Options.compression_opts.zstd_max_train_bytes: 0
2025/10/02-02:32:57.363196 500         Options.compression_opts.use_zstd_dict_trainer: true
2025/10/02-02:32:57.363199 500         Options.compression_opts.parallel_threads: 1
2025/10/02-02:32:57.363202 500                  Options.compression_opts.enabled: false
2025/10/02-02:32:57.363205 500         Options.compression_opts.max_dict_buffer_bytes: 0
2025/10/02-02:32:57.363234 500      Options.level0_file_num_compaction_trigger: 4
2025/10/02-02:32:57.363239 500          Options.level0_slowdown_writes_trigger: 20
2025/10/02-02:32:57.363242 500              Options.level0_stop_writes_trigger: 36
2025/10/02-02:32:57.363244 500                   Options.target_file_size_base: 67108864
2025/10/02-02:32:57.363250 500             Options.target_file_size_multiplier: 1
2025/10/02-02:32:57.363254 500                Options.max_bytes_for_level_base: 268435456
2025/10/02-02:32:57.363257 500 Options.level_compaction_dynamic_level_bytes: 1
2025/10/02-02:32:57.363262 500          Options.max_bytes_for_level_multiplier: 10.000000
2025/10/02-02:32:57.363266 500 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/10/02-02:32:57.363269 500 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/10/02-02:32:57.363271 500 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/10/02-02:32:57.363273 500 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/10/02-02:32:57.363274 500 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/10/02-02:32:57.363276 500 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/10/02-02:32:57.363278 500 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/10/02-02:32:57.363282 500       Options.max_sequential_skip_in_iterations: 8
2025/10/02-02:32:57.363284 500                    Options.max_compaction_bytes: 1677721600
2025/10/02-02:32:57.363286 500                        Options.arena_block_size: 1048576
2025/10/02-02:32:57.363288 500   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/10/02-02:32:57.363289 500   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/10/02-02:32:57.363291 500                Options.disable_auto_compactions: 0
2025/10/02-02:32:57.363295 500                        Options.compaction_style: kCompactionStyleLevel
2025/10/02-02:32:57.363297 500                          Options.compaction_pri: kMinOverlappingRatio
2025/10/02-02:32:57.363299 500 Options.compaction_options_universal.size_ratio: 1
2025/10/02-02:32:57.363300 500 Options.compaction_options_universal.min_merge_width: 2
2025/10/02-02:32:57.363302 500 Options.compaction_options_universal.max_merge_width: 4294967295
2025/10/02-02:32:57.363304 500 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/10/02-02:32:57.363306 500 Options.compaction_options_universal.compression_size_percent: -1
2025/10/02-02:32:57.363308 500 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/10/02-02:32:57.363310 500 Options.compaction_options_universal.max_read_amp: -1
2025/10/02-02:32:57.363311 500 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/10/02-02:32:57.363313 500 Options.compaction_options_fifo.allow_compaction: 0
2025/10/02-02:32:57.363350 500                   Options.table_properties_collectors: 
2025/10/02-02:32:57.363354 500                   Options.inplace_update_support: 0
2025/10/02-02:32:57.363356 500                 Options.inplace_update_num_locks: 10000
2025/10/02-02:32:57.363359 500               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/10/02-02:32:57.363360 500               Options.memtable_whole_key_filtering: 0
2025/10/02-02:32:57.363362 500   Options.memtable_huge_page_size: 0
2025/10/02-02:32:57.363364 500                           Options.bloom_locality: 0
2025/10/02-02:32:57.363365 500                    Options.max_successive_merges: 0
2025/10/02-02:32:57.363376 500             Options.strict_max_successive_merges: 0
2025/10/02-02:32:57.363377 500                Options.optimize_filters_for_hits: 0
2025/10/02-02:32:57.363379 500                Options.paranoid_file_checks: 0
2025/10/02-02:32:57.363380 500                Options.force_consistency_checks: 1
2025/10/02-02:32:57.363382 500                Options.report_bg_io_stats: 0
2025/10/02-02:32:57.363383 500                               Options.ttl: 2592000
2025/10/02-02:32:57.363385 500          Options.periodic_compaction_seconds: 0
2025/10/02-02:32:57.363387 500                        Options.default_temperature: kUnknown
2025/10/02-02:32:57.363389 500  Options.preclude_last_level_data_seconds: 0
2025/10/02-02:32:57.363390 500    Options.preserve_internal_time_seconds: 0
2025/10/02-02:32:57.363392 500                       Options.enable_blob_files: false
2025/10/02-02:32:57.363394 500                           Options.min_blob_size: 0
2025/10/02-02:32:57.363395 500                          Options.blob_file_size: 268435456
2025/10/02-02:32:57.363397 500                   Options.blob_compression_type: NoCompression
2025/10/02-02:32:57.363399 500          Options.enable_blob_garbage_collection: false
2025/10/02-02:32:57.363401 500      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/10/02-02:32:57.363403 500 Options.blob_garbage_collection_force_threshold: 1.000000
2025/10/02-02:32:57.363405 500          Options.blob_compaction_readahead_size: 0
2025/10/02-02:32:57.363407 500                Options.blob_file_starting_level: 0
2025/10/02-02:32:57.363409 500         Options.experimental_mempurge_threshold: 0.000000
2025/10/02-02:32:57.363410 500            Options.memtable_max_range_deletions: 0
2025/10/02-02:32:57.476621 500 DB pointer 0x7150b4e71800
